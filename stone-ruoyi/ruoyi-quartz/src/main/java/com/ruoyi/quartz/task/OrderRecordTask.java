package com.ruoyi.quartz.task;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.*;
import com.ruoyi.system.enums.OrderStatusEnum;
import com.ruoyi.system.mapper.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025-08-12
 * @description: 异步处理所有抢购完成的订单
 */
@Slf4j
@Component("OrderRecordTask")
public class OrderRecordTask {

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    @Autowired
    private HtOrderMapper htOrderMapper;

    @Autowired
    private HtOrderRecordMapper htOrderRecordMapper;

    @Autowired
    private AppUserMapper appUserMapper;

    //今日订单，根据算力进行收付款记录的生成
    public void computingPower() {
        if(shouldTriggerAlgorithm()){
            executeAlgorithmAsync();
        }
    }

    /**
     * 检查是否需要触发算法
     */
    private boolean shouldTriggerAlgorithm() {
        List<HtGoods> goodsList = htGoodsMapper.selectList(Wrappers.<HtGoods>query()
                .eq("is_show", 1)
                .eq("is_del", 0));
        return goodsList.isEmpty();
    }
    /**
     * 执行算法
     */
    @Transactional
    public void executeAlgorithmAsync() {
        try {
            log.info("开始执行算力匹配算法");

            // 查询今天所有待处理的付款订单
            List<HtOrder> orderList = htOrderMapper.selectList(Wrappers.<HtOrder>query()
                    .like("created_at", DateUtils.getDate())
                    .eq("order_status", OrderStatusEnum.PENDING_REVIEW.getCode()));

            if (orderList.isEmpty()) {
                log.info("今日无待处理订单");
                return;
            }
            log.info("今日待处理付款订单数量: {}", orderList.size());

            // 执行付款优化算法，避免交叉付款
            optimizePaymentOrders(orderList);

        } catch (Exception e) {
            log.error("执行算法异常", e);
        }
    }

    /**
     * 优化付款订单算法 - 避免用户之间交叉付款
     * 规则：所有付款订单需要进行匹配，避免相互交叉付款，减少用户的付款次数
     */
    private void optimizePaymentOrders(List<HtOrder> orderList) {
        log.info("开始执行付款优化算法");

        // 按用户分组统计付款和收款金额
        Map<Long, BigDecimal> userPaymentMap = new HashMap<>(); // 用户需要付款的总额
        Map<Long, BigDecimal> userReceiveMap = new HashMap<>(); // 用户需要收款的总额

        // 统计每个用户的付款总额（作为付款人）
        for (HtOrder order : orderList) {
            userPaymentMap.merge(order.getUserId(), order.getAmount(), BigDecimal::add);
        }

        // 统计每个用户的收款总额（作为收款人）
        for (HtOrder order : orderList) {
            if (order.getRecipientId() != null) {
                userReceiveMap.merge(order.getRecipientId().longValue(), order.getAmount(), BigDecimal::add);
            }
        }

        log.info("用户付款统计: {}", userPaymentMap);
        log.info("用户收款统计: {}", userReceiveMap);

        // 计算每个用户的净付款金额（付款 - 收款）
        Map<Long, BigDecimal> netPaymentMap = calculateNetPayments(userPaymentMap, userReceiveMap);

        // 处理特殊情况和生成优化后的付款记录
        processSpecialCasesAndGenerateRecords(netPaymentMap, orderList);
    }

    /**
     * 计算每个用户的净付款金额
     */
    private Map<Long, BigDecimal> calculateNetPayments(Map<Long, BigDecimal> userPaymentMap,
                                                       Map<Long, BigDecimal> userReceiveMap) {
        Map<Long, BigDecimal> netPaymentMap = new HashMap<>();

        // 所有涉及的用户ID
        Set<Long> allUserIds = new HashSet<>();
        allUserIds.addAll(userPaymentMap.keySet());
        allUserIds.addAll(userReceiveMap.keySet());

        for (Long userId : allUserIds) {
            BigDecimal payment = userPaymentMap.getOrDefault(userId, BigDecimal.ZERO);
            BigDecimal receive = userReceiveMap.getOrDefault(userId, BigDecimal.ZERO);
            BigDecimal netPayment = payment.subtract(receive);
            netPaymentMap.put(userId, netPayment);
        }

        log.info("用户净付款统计: {}", netPaymentMap);
        return netPaymentMap;
    }

    /**
     * 处理特殊情况和生成优化后的付款记录
     */
    private void processSpecialCasesAndGenerateRecords(Map<Long, BigDecimal> netPaymentMap, List<HtOrder> orderList) {
        // 分离不同类型的用户
        List<Long> noPaymentUsers = new ArrayList<>(); // 无需付款的用户
        List<Map.Entry<Long, BigDecimal>> netPayers = new ArrayList<>(); // 净付款用户
        List<Map.Entry<Long, BigDecimal>> netReceivers = new ArrayList<>(); // 净收款用户


        for (Map.Entry<Long, BigDecimal> entry : netPaymentMap.entrySet()) {
            BigDecimal netAmount = entry.getValue();
            if (netAmount.compareTo(BigDecimal.ZERO) == 0) {
                // 净付款为0，无需付款
                noPaymentUsers.add(entry.getKey());
            } else if (netAmount.compareTo(BigDecimal.ZERO) > 0) {
                // 需要付款
                netPayers.add(entry);
            } else {
                // 需要收款
                netReceivers.add(new AbstractMap.SimpleEntry<>(entry.getKey(), netAmount.abs()));
            }
        }

        log.info("无需付款用户数量: {}, 净付款用户数量: {}, 净收款用户数量: {}",
                noPaymentUsers.size(), netPayers.size(), netReceivers.size());

        // 处理无需付款的订单
        processNoPaymentOrders(noPaymentUsers, orderList);

        // 检查是否存在相互付款金额一致的情况
        processEqualPaymentOrders(netPayers, netReceivers, orderList);

        // 处理剩余的付款匹配
        if (!netPayers.isEmpty() && !netReceivers.isEmpty()) {
            executeOptimalMatching(netPayers, netReceivers, orderList);
        }

        // 更新剩余订单状态
        updateRemainingOrderStatuses(orderList);
    }

    /**
     * 处理无需付款的订单
     * 如果用户净付款金额为0，直接修改订单状态为"订单已支付 待确认收款"
     */
    private void processNoPaymentOrders(List<Long> noPaymentUsers, List<HtOrder> orderList) {
        for (Long userId : noPaymentUsers) {
            List<HtOrder> userOrders = orderList.stream()
                    .filter(order -> order.getUserId().equals(userId))
                    .collect(Collectors.toList());

            for (HtOrder order : userOrders) {
                order.setOrderStatus(OrderStatusEnum.PAID_PENDING_CONFIRMATION.getCode());
                order.setUpdatedAt(new Date());
                order.setMatchedTime(new Date());
                order.setPaymentTime(new Date()); // 设置付款时间
                htOrderMapper.updateById(order);

                log.info("无需付款订单处理完成: 订单ID={}, 用户ID={}, 状态={}",
                        order.getId(), userId, OrderStatusEnum.PAID_PENDING_CONFIRMATION.getDescription());
            }
        }
    }

    /**
     * 处理相互付款金额一致的订单
     * 如果两个用户相互付款且金额一致，无需生成付款记录，直接修改订单状态为"订单已确认收款 待委托上架
     */
    private void processEqualPaymentOrders(List<Map.Entry<Long, BigDecimal>> netPayers,
                                           List<Map.Entry<Long, BigDecimal>> netReceivers,
                                           List<HtOrder> orderList) {

        Iterator<Map.Entry<Long, BigDecimal>> payerIterator = netPayers.iterator();

        while (payerIterator.hasNext()) {
            Map.Entry<Long, BigDecimal> payer = payerIterator.next();

            // 查找金额相等的收款用户
            Iterator<Map.Entry<Long, BigDecimal>> receiverIterator = netReceivers.iterator();
            while (receiverIterator.hasNext()) {
                Map.Entry<Long, BigDecimal> receiver = receiverIterator.next();

                if (payer.getValue().compareTo(receiver.getValue()) == 0) {
                    // 找到金额相等的配对
                    Long payerUserId = payer.getKey();
                    Long receiverUserId = receiver.getKey();
                    BigDecimal amount = payer.getValue();

                    log.info("发现相互付款金额一致: 用户{} 和用户{}, 金额={}", payerUserId, receiverUserId, amount);

                    // 更新相关订单状态为已确认收款
                    updateEqualPaymentOrderStatuses(payerUserId, receiverUserId, orderList);

                    // 从列表中移除已处理的用户
                    payerIterator.remove();
                    receiverIterator.remove();
                    break;
                }
            }
        }
    }

    /**
     * 执行最优匹配算法
     */
    private void executeOptimalMatching(List<Map.Entry<Long, BigDecimal>> netPayers,
                                        List<Map.Entry<Long, BigDecimal>> netReceivers,
                                        List<HtOrder> orderList) {

        int payerIndex = 0;
        int receiverIndex = 0;

        while (payerIndex < netPayers.size() && receiverIndex < netReceivers.size()) {
            Map.Entry<Long, BigDecimal> payer = netPayers.get(payerIndex);
            Map.Entry<Long, BigDecimal> receiver = netReceivers.get(receiverIndex);

            Long payerUserId = payer.getKey();
            Long receiverUserId = receiver.getKey();
            BigDecimal payerAmount = payer.getValue();
            BigDecimal receiverAmount = receiver.getValue();

            // 计算实际转账金额（取较小值）
            BigDecimal transferAmount = payerAmount.min(receiverAmount);

            log.info("匹配付款: 用户{} 向用户{} 付款 {}", payerUserId, receiverUserId, transferAmount);

            // 创建订单记录
            createOptimizedOrderRecord(payerUserId, receiverUserId, transferAmount, orderList);

            // 更新剩余金额
            BigDecimal remainingPayerAmount = payerAmount.subtract(transferAmount);
            BigDecimal remainingReceiverAmount = receiverAmount.subtract(transferAmount);

            if (remainingPayerAmount.compareTo(BigDecimal.ZERO) == 0) {
                payerIndex++;
            } else {
                payer.setValue(remainingPayerAmount);
            }

            if (remainingReceiverAmount.compareTo(BigDecimal.ZERO) == 0) {
                receiverIndex++;
            } else {
                receiver.setValue(remainingReceiverAmount);
            }
        }
    }

    /**
     * 更新相互付款金额一致的订单状态
     */
    private void updateEqualPaymentOrderStatuses(Long payerUserId, Long receiverUserId, List<HtOrder> orderList) {
        // 更新付款用户的订单状态
        List<HtOrder> payerOrders = orderList.stream()
                .filter(order -> order.getUserId().equals(payerUserId))
                .collect(Collectors.toList());

        for (HtOrder order : payerOrders) {
            order.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
            order.setUpdatedAt(new Date());
            order.setMatchedTime(new Date());
            order.setPaymentTime(new Date());
            htOrderMapper.updateById(order);

            log.info("相互付款一致订单处理完成: 订单ID={}, 付款用户ID={}, 状态={}",
                    order.getId(), payerUserId, OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getDescription());
        }

        // 更新收款用户的订单状态
        List<HtOrder> receiverOrders = orderList.stream()
                .filter(order -> order.getRecipientId() != null && order.getRecipientId().equals(receiverUserId.intValue()))
                .collect(Collectors.toList());

        for (HtOrder order : receiverOrders) {
            order.setOrderStatus(OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getCode());
            order.setUpdatedAt(new Date());
            order.setMatchedTime(new Date());
            order.setPaymentTime(new Date());
            htOrderMapper.updateById(order);

            log.info("相互付款一致订单处理完成: 订单ID={}, 收款用户ID={}, 状态={}",
                    order.getId(), receiverUserId, OrderStatusEnum.CONFIRMED_PENDING_CONSIGNMENT.getDescription());
        }
    }

    /**
     * 更新剩余订单状态
     */
    private void updateRemainingOrderStatuses(List<HtOrder> orderList) {
        for (HtOrder order : orderList) {
            // 只更新状态仍为待审核的订单
            if (OrderStatusEnum.PENDING_REVIEW.getCode().equals(order.getOrderStatus())) {
                order.setOrderStatus(OrderStatusEnum.ALLOCATION_COMPLETED.getCode());
                order.setUpdatedAt(new Date());
                order.setMatchedTime(new Date());
                htOrderMapper.updateById(order);

                log.info("更新剩余订单状态: 订单ID={}, 状态={}",
                        order.getId(), OrderStatusEnum.ALLOCATION_COMPLETED.getDescription());
            }
        }
    }



    /**
     * 创建优化后的订单记录
     */
    private void createOptimizedOrderRecord(Long payerUserId, Long receiverUserId, BigDecimal amount, List<HtOrder> orderList) {
        try {
            // 获取付款用户信息
            AppUser payerUser = appUserMapper.selectById(payerUserId);
            AppUser receiverUser = appUserMapper.selectById(receiverUserId);

            if (payerUser == null || receiverUser == null) {
                log.error("用户信息不存在: 付款用户ID={}, 收款用户ID={}", payerUserId, receiverUserId);
                return;
            }

            // 创建订单记录
            HtOrderRecord orderRecord = new HtOrderRecord();
            orderRecord.setUserId(payerUserId.intValue());
            orderRecord.setRecipientId(receiverUserId.intValue());
            orderRecord.setRecipientName(receiverUser.getRealName());
            orderRecord.setRecipientPhone(receiverUser.getPhone());
            orderRecord.setAmount(amount);
            orderRecord.setType(0); // 0未确认
            orderRecord.setCreateTime(new Date());

            // 查找付款用户相关的订单ID
            HtOrder relatedOrder = orderList.stream()
                    .filter(order -> order.getUserId().equals(payerUserId))
                    .findFirst()
                    .orElse(null);

            if (relatedOrder != null) {
                orderRecord.setOrderId(relatedOrder.getId().intValue());
            }

            htOrderRecordMapper.insert(orderRecord);

            log.info("创建优化订单记录成功: 付款用户={}, 收款用户={}, 金额={}",
                    payerUser.getRealName(), receiverUser.getRealName(), amount);

        } catch (Exception e) {
            log.error("创建优化订单记录异常: 付款用户ID={}, 收款用户ID={}, 金额={}",
                    payerUserId, receiverUserId, amount, e);
        }
    }




}